declare global {
  const MOCK_CODES: typeof import('../../utils/mock-data')['MOCK_CODES']
  const MOCK_MENUS: typeof import('../../utils/mock-data')['MOCK_MENUS']
  const MOCK_MENU_LIST: typeof import('../../utils/mock-data')['MOCK_MENU_LIST']
  const MOCK_USERS: typeof import('../../utils/mock-data')['MOCK_USERS']
  const appendCorsHeaders: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['appendCorsHeaders']
  const appendCorsPreflightHeaders: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['appendCorsPreflightHeaders']
  const appendHeader: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['appendHeader']
  const appendHeaders: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['appendHeaders']
  const appendResponseHeader: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['appendResponseHeader']
  const appendResponseHeaders: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['appendResponseHeaders']
  const assertMethod: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['assertMethod']
  const cachedEventHandler: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/cache')['cachedEventHandler']
  const cachedFunction: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/cache')['cachedFunction']
  const callNodeListener: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['callNodeListener']
  const clearRefreshTokenCookie: typeof import('../../utils/cookie-utils')['clearRefreshTokenCookie']
  const clearResponseHeaders: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['clearResponseHeaders']
  const clearSession: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['clearSession']
  const createApp: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['createApp']
  const createAppEventHandler: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['createAppEventHandler']
  const createError: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['createError']
  const createEvent: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['createEvent']
  const createEventStream: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['createEventStream']
  const createRouter: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['createRouter']
  const defaultContentType: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['defaultContentType']
  const defineCachedEventHandler: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/cache')['defineCachedEventHandler']
  const defineCachedFunction: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/cache')['defineCachedFunction']
  const defineEventHandler: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['defineEventHandler']
  const defineLazyEventHandler: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['defineLazyEventHandler']
  const defineNitroErrorHandler: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/error/utils')['defineNitroErrorHandler']
  const defineNitroPlugin: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/plugin')['defineNitroPlugin']
  const defineNodeListener: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['defineNodeListener']
  const defineNodeMiddleware: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['defineNodeMiddleware']
  const defineRenderHandler: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/renderer')['defineRenderHandler']
  const defineRequestMiddleware: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['defineRequestMiddleware']
  const defineResponseMiddleware: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['defineResponseMiddleware']
  const defineRouteMeta: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/meta')['defineRouteMeta']
  const defineTask: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/task')['defineTask']
  const defineWebSocket: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['defineWebSocket']
  const defineWebSocketHandler: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['defineWebSocketHandler']
  const deleteCookie: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['deleteCookie']
  const dynamicEventHandler: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['dynamicEventHandler']
  const eventHandler: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['eventHandler']
  const fetchWithEvent: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['fetchWithEvent']
  const forbiddenResponse: typeof import('../../utils/response')['forbiddenResponse']
  const fromNodeMiddleware: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['fromNodeMiddleware']
  const fromPlainHandler: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['fromPlainHandler']
  const fromWebHandler: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['fromWebHandler']
  const generateAccessToken: typeof import('../../utils/jwt-utils')['generateAccessToken']
  const generateRefreshToken: typeof import('../../utils/jwt-utils')['generateRefreshToken']
  const getCookie: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getCookie']
  const getHeader: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getHeader']
  const getHeaders: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getHeaders']
  const getMenuIds: typeof import('../../utils/mock-data')['getMenuIds']
  const getMethod: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getMethod']
  const getProxyRequestHeaders: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getProxyRequestHeaders']
  const getQuery: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getQuery']
  const getRefreshTokenFromCookie: typeof import('../../utils/cookie-utils')['getRefreshTokenFromCookie']
  const getRequestFingerprint: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getRequestFingerprint']
  const getRequestHeader: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getRequestHeader']
  const getRequestHeaders: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getRequestHeaders']
  const getRequestHost: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getRequestHost']
  const getRequestIP: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getRequestIP']
  const getRequestPath: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getRequestPath']
  const getRequestProtocol: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getRequestProtocol']
  const getRequestURL: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getRequestURL']
  const getRequestWebStream: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getRequestWebStream']
  const getResponseHeader: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getResponseHeader']
  const getResponseHeaders: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getResponseHeaders']
  const getResponseStatus: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getResponseStatus']
  const getResponseStatusText: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getResponseStatusText']
  const getRouteRules: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/route-rules')['getRouteRules']
  const getRouterParam: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getRouterParam']
  const getRouterParams: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getRouterParams']
  const getSession: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getSession']
  const getValidatedQuery: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getValidatedQuery']
  const getValidatedRouterParams: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['getValidatedRouterParams']
  const handleCacheHeaders: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['handleCacheHeaders']
  const handleCors: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['handleCors']
  const isCorsOriginAllowed: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['isCorsOriginAllowed']
  const isError: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['isError']
  const isEvent: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['isEvent']
  const isEventHandler: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['isEventHandler']
  const isMethod: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['isMethod']
  const isPreflightRequest: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['isPreflightRequest']
  const isStream: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['isStream']
  const isWebResponse: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['isWebResponse']
  const lazyEventHandler: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['lazyEventHandler']
  const nitroPlugin: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/plugin')['nitroPlugin']
  const pagination: typeof import('../../utils/response')['pagination']
  const parseCookies: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['parseCookies']
  const promisifyNodeListener: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['promisifyNodeListener']
  const proxyRequest: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['proxyRequest']
  const readBody: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['readBody']
  const readFormData: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['readFormData']
  const readMultipartFormData: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['readMultipartFormData']
  const readRawBody: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['readRawBody']
  const readValidatedBody: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['readValidatedBody']
  const removeResponseHeader: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['removeResponseHeader']
  const runTask: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/task')['runTask']
  const sanitizeStatusCode: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['sanitizeStatusCode']
  const sanitizeStatusMessage: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['sanitizeStatusMessage']
  const sealSession: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['sealSession']
  const send: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['send']
  const sendError: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['sendError']
  const sendIterable: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['sendIterable']
  const sendNoContent: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['sendNoContent']
  const sendProxy: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['sendProxy']
  const sendRedirect: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['sendRedirect']
  const sendStream: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['sendStream']
  const sendWebResponse: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['sendWebResponse']
  const serveStatic: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['serveStatic']
  const setCookie: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['setCookie']
  const setHeader: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['setHeader']
  const setHeaders: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['setHeaders']
  const setRefreshTokenCookie: typeof import('../../utils/cookie-utils')['setRefreshTokenCookie']
  const setResponseHeader: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['setResponseHeader']
  const setResponseHeaders: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['setResponseHeaders']
  const setResponseStatus: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['setResponseStatus']
  const sleep: typeof import('../../utils/response')['sleep']
  const splitCookiesString: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['splitCookiesString']
  const toEventHandler: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['toEventHandler']
  const toNodeListener: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['toNodeListener']
  const toPlainHandler: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['toPlainHandler']
  const toWebHandler: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['toWebHandler']
  const toWebRequest: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['toWebRequest']
  const unAuthorizedResponse: typeof import('../../utils/response')['unAuthorizedResponse']
  const unsealSession: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['unsealSession']
  const updateSession: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['updateSession']
  const useAppConfig: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/config')['useAppConfig']
  const useBase: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['useBase']
  const useEvent: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/context')['useEvent']
  const useNitroApp: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/app')['useNitroApp']
  const usePageResponseSuccess: typeof import('../../utils/response')['usePageResponseSuccess']
  const useResponseError: typeof import('../../utils/response')['useResponseError']
  const useResponseSuccess: typeof import('../../utils/response')['useResponseSuccess']
  const useRuntimeConfig: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/config')['useRuntimeConfig']
  const useSession: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['useSession']
  const useStorage: typeof import('../../../../node_modules/.pnpm/nitropack@2.11.8_encoding@0.1.13/node_modules/nitropack/dist/runtime/internal/storage')['useStorage']
  const verifyAccessToken: typeof import('../../utils/jwt-utils')['verifyAccessToken']
  const verifyRefreshToken: typeof import('../../utils/jwt-utils')['verifyRefreshToken']
  const writeEarlyHints: typeof import('../../../../node_modules/.pnpm/h3@1.15.1/node_modules/h3')['writeEarlyHints']
}
// for type re-export
declare global {
  // @ts-ignore
  export type { UserPayload } from '../../utils/jwt-utils'
  import('../../utils/jwt-utils')
  // @ts-ignore
  export type { UserInfo } from '../../utils/mock-data'
  import('../../utils/mock-data')
}
export { useNitroApp } from 'nitropack/runtime/internal/app';
export { useRuntimeConfig, useAppConfig } from 'nitropack/runtime/internal/config';
export { defineNitroPlugin, nitroPlugin } from 'nitropack/runtime/internal/plugin';
export { defineCachedFunction, defineCachedEventHandler, cachedFunction, cachedEventHandler } from 'nitropack/runtime/internal/cache';
export { useStorage } from 'nitropack/runtime/internal/storage';
export { defineRenderHandler } from 'nitropack/runtime/internal/renderer';
export { defineRouteMeta } from 'nitropack/runtime/internal/meta';
export { getRouteRules } from 'nitropack/runtime/internal/route-rules';
export { useEvent } from 'nitropack/runtime/internal/context';
export { defineTask, runTask } from 'nitropack/runtime/internal/task';
export { defineNitroErrorHandler } from 'nitropack/runtime/internal/error/utils';
export { appendCorsHeaders, appendCorsPreflightHeaders, appendHeader, appendHeaders, appendResponseHeader, appendResponseHeaders, assertMethod, callNodeListener, clearResponseHeaders, clearSession, createApp, createAppEventHandler, createError, createEvent, createEventStream, createRouter, defaultContentType, defineEventHandler, defineLazyEventHandler, defineNodeListener, defineNodeMiddleware, defineRequestMiddleware, defineResponseMiddleware, defineWebSocket, defineWebSocketHandler, deleteCookie, dynamicEventHandler, eventHandler, fetchWithEvent, fromNodeMiddleware, fromPlainHandler, fromWebHandler, getCookie, getHeader, getHeaders, getMethod, getProxyRequestHeaders, getQuery, getRequestFingerprint, getRequestHeader, getRequestHeaders, getRequestHost, getRequestIP, getRequestPath, getRequestProtocol, getRequestURL, getRequestWebStream, getResponseHeader, getResponseHeaders, getResponseStatus, getResponseStatusText, getRouterParam, getRouterParams, getSession, getValidatedQuery, getValidatedRouterParams, handleCacheHeaders, handleCors, isCorsOriginAllowed, isError, isEvent, isEventHandler, isMethod, isPreflightRequest, isStream, isWebResponse, lazyEventHandler, parseCookies, promisifyNodeListener, proxyRequest, readBody, readFormData, readMultipartFormData, readRawBody, readValidatedBody, removeResponseHeader, sanitizeStatusCode, sanitizeStatusMessage, sealSession, send, sendError, sendIterable, sendNoContent, sendProxy, sendRedirect, sendStream, sendWebResponse, serveStatic, setCookie, setHeader, setHeaders, setResponseHeader, setResponseHeaders, setResponseStatus, splitCookiesString, toEventHandler, toNodeListener, toPlainHandler, toWebHandler, toWebRequest, unsealSession, updateSession, useBase, useSession, writeEarlyHints } from 'h3';
export { clearRefreshTokenCookie, setRefreshTokenCookie, getRefreshTokenFromCookie } from 'E:/software_competition/SourceCode/frontend/subject_a7/apps/backend-mock/utils/cookie-utils';
export { generateAccessToken, generateRefreshToken, verifyAccessToken, verifyRefreshToken } from 'E:/software_competition/SourceCode/frontend/subject_a7/apps/backend-mock/utils/jwt-utils';
export { MOCK_USERS, MOCK_CODES, MOCK_MENUS, MOCK_MENU_LIST, getMenuIds } from 'E:/software_competition/SourceCode/frontend/subject_a7/apps/backend-mock/utils/mock-data';
export { useResponseSuccess, usePageResponseSuccess, useResponseError, forbiddenResponse, unAuthorizedResponse, sleep, pagination } from 'E:/software_competition/SourceCode/frontend/subject_a7/apps/backend-mock/utils/response';