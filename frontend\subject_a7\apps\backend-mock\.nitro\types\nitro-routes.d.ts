// Generated by nitro
import type { Serialize, Simplify } from "nitropack/types";
declare module "nitropack/types" {
  type Awaited<T> = T extends PromiseLike<infer U> ? Awaited<U> : T
  interface InternalApi {
    '/api/auth/codes': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/codes').default>>>>
    }
    '/api/auth/login': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/login.post').default>>>>
    }
    '/api/auth/logout': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/logout.post').default>>>>
    }
    '/api/auth/refresh': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/auth/refresh.post').default>>>>
    }
    '/api/menu/all': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/menu/all').default>>>>
    }
    '/api/status': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/status').default>>>>
    }
    '/api/system/dept/': {
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/dept/.post').default>>>>
    }
    '/api/system/dept/:id': {
      'delete': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/dept/[id].delete').default>>>>
      'put': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/dept/[id].put').default>>>>
    }
    '/api/system/dept/list': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/dept/list').default>>>>
    }
    '/api/system/menu/list': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/menu/list').default>>>>
    }
    '/api/system/menu/name-exists': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/menu/name-exists').default>>>>
    }
    '/api/system/menu/path-exists': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/menu/path-exists').default>>>>
    }
    '/api/system/role/list': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/system/role/list').default>>>>
    }
    '/api/table/list': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/table/list').default>>>>
    }
    '/api/test': {
      'get': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/test.get').default>>>>
      'post': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/test.post').default>>>>
    }
    '/api/user/info': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../api/user/info').default>>>>
    }
    '/**': {
      'default': Simplify<Serialize<Awaited<ReturnType<typeof import('../../routes/[...]').default>>>>
    }
  }
}
export {}