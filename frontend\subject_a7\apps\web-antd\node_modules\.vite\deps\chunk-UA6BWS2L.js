import {
  tooltip_default
} from "./chunk-E2YKSDJE.js";
import {
  dynamicApp
} from "./chunk-URMRKTZM.js";
import {
  VxeUI
} from "./chunk-BCXSQRR2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/tooltip/index.js
var VxeTooltip = Object.assign({}, tooltip_default, {
  install(app) {
    app.component(tooltip_default.name, tooltip_default);
  }
});
dynamicApp.use(VxeTooltip);
VxeUI.component(tooltip_default);
var Tooltip = VxeTooltip;
var tooltip_default2 = VxeTooltip;

export {
  VxeTooltip,
  Tooltip,
  tooltip_default2 as tooltip_default
};
//# sourceMappingURL=chunk-UA6BWS2L.js.map
