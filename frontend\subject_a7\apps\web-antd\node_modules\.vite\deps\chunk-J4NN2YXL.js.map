{"version": 3, "sources": ["../../../../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/button/index.js"], "sourcesContent": ["import { VxeUI } from '@vxe-ui/core';\nimport VxeButtonComponent from './src/button';\nimport { dynamicApp } from '../dynamics';\nexport const VxeButton = Object.assign({}, VxeButtonComponent, {\n    install(app) {\n        app.component(VxeButtonComponent.name, VxeButtonComponent);\n    }\n});\ndynamicApp.use(VxeButton);\nVxeUI.component(VxeButtonComponent);\nexport const Button = VxeButton;\nexport default VxeButton;\n"], "mappings": ";;;;;;;;;;;AAGO,IAAM,YAAY,OAAO,OAAO,CAAC,GAAG,gBAAoB;AAAA,EAC3D,QAAQ,KAAK;AACT,QAAI,UAAU,eAAmB,MAAM,cAAkB;AAAA,EAC7D;AACJ,CAAC;AACD,WAAW,IAAI,SAAS;AACxB,MAAM,UAAU,cAAkB;AAC3B,IAAM,SAAS;AACtB,IAAOA,kBAAQ;", "names": ["button_default"]}