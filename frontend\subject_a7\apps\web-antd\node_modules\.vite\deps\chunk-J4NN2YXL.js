import {
  button_default
} from "./chunk-QVIYMD4W.js";
import {
  dynamicApp
} from "./chunk-URMRKTZM.js";
import {
  VxeUI
} from "./chunk-BCXSQRR2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/button/index.js
var VxeButton = Object.assign({}, button_default, {
  install(app) {
    app.component(button_default.name, button_default);
  }
});
dynamicApp.use(VxeButton);
VxeUI.component(button_default);
var Button = VxeButton;
var button_default2 = VxeButton;

export {
  VxeButton,
  Button,
  button_default2 as button_default
};
//# sourceMappingURL=chunk-J4NN2YXL.js.map
