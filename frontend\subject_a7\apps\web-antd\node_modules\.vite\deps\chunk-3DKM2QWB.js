import {
  input_default
} from "./chunk-5R5SEXRV.js";
import {
  dynamicApp
} from "./chunk-URMRKTZM.js";
import {
  VxeUI
} from "./chunk-BCXSQRR2.js";

// ../../node_modules/.pnpm/vxe-pc-ui@4.5.5_vue@3.5.13_typescript@5.8.2_/node_modules/vxe-pc-ui/es/input/index.js
var VxeInput = Object.assign(input_default, {
  install(app) {
    app.component(input_default.name, input_default);
  }
});
dynamicApp.use(VxeInput);
VxeUI.component(input_default);
var Input = VxeInput;
var input_default2 = VxeInput;

export {
  VxeInput,
  Input,
  input_default2 as input_default
};
//# sourceMappingURL=chunk-3DKM2QWB.js.map
